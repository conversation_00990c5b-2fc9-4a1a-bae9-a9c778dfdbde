package net.summerfarm.payment.routing.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.payment.routing.model.dto.*;

import java.util.List;

/**
 * @description: 支付渠道服务类
 * @author: George
 * @date: 2024-11-26
 **/
public interface PaymentChannelService {

    /**
     * 分页查询支付渠道
     * @param query
     * @return
     */
    PageInfo<PaymentChannelListDTO> pageListChannel(PaymentChannelQueryDTO query);

    /**
     * 查询支付渠道
     * @param query
     * @return
     */
    List<PaymentChannelListDTO> listChannel(PaymentChannelQueryDTO query);

    /**
     * 查询支付渠道详情
     * @param paymentChannelIdDTO
     * @return
     */
    PaymentChannelDetailDTO queryChannelDetail(PaymentChannelIdDTO paymentChannelIdDTO);

    /**
     * 保存支付渠道
     * @param paymentChannelSaveDTO
     * @return
     */
    Long saveChannel(PaymentChannelSaveDTO paymentChannelSaveDTO);

    /**
     * 修改支付渠道状态
     * @param paymentChannelStatusDTO
     */
    boolean changeStatus(PaymentChannelStatusDTO paymentChannelStatusDTO);

    /**
     * 保存SaaS业务线支付渠道（包含渠道、规则、路由一体化配置）
     * @param paymentChannelSaveDTO
     * @return
     */
    Long saveChannel4SaaS(PaymentChannelSaveDTO paymentChannelSaveDTO);

    /**
     * 批量保存SaaS业务线支付渠道（包含渠道、规则、路由一体化配置）
     * @param batchSaveDTO
     * @return 成功保存的渠道数量
     */
    Integer batchSaveChannel4SaaS(PaymentChannelBatchSaveDTO batchSaveDTO);

    /**
     * 查询支付渠道公司实体
     * @param paymentChannelCompanyEntityQueryDTO
     * @return
     */
    List<String> queryCompanyEntities(PaymentChannelCompanyEntityQueryDTO paymentChannelCompanyEntityQueryDTO);

    /**
     * 根据ID查询支付渠道
     * @param id
     * @return
     */
    PaymentChannelTiledDTO selectById(Long id);

}
